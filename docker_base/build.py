import configparser
import enum
import os
import re
import shutil
import yaml
from pathlib import Path
import glob
import subprocess
import argparse
import jinja2
from typing import Callable, Optional, Dict, List, Any
import xxhash
from dotenv import load_dotenv, dotenv_values


def load_config(config_path: str) -> dict:
    """Load the mapping configuration from a JSON file."""
    with open(config_path, "r") as file:
        return yaml.safe_load(file)


def parse_env_file(env_file):
    """Parse an .env file into a dictionary"""
    env_dict = {}
    with open(env_file, "r") as file:
        for line in file:
            line = line.strip()
            if line and not line.startswith("#"):  # Ignore comments and empty lines
                key, value = line.split("=", 1)
                env_dict[key.strip()] = value.strip()
    return env_dict


def dict_deep_merge(base: Dict[str, Any], to_merge: Dict[str, Any]):
    base.update(
        {
            key: dict_deep_merge(base[key], value)
            if isinstance(base.get(key), dict) and isinstance(value, dict)
            else value
            for key, value in to_merge.items()
        }
    )
    return base


def append_file_contents(dest_path: str, src_path: str) -> None:
    with open(src_path, "r") as src_file:
        with open(dest_path, "a") as dest_file:
            dest_file.write(src_file.read())


def delete_build_directory(build_dir: str):
    """Delete the build directory if it exists."""
    build_path = Path(build_dir)
    if build_path.exists() and build_path.is_dir():
        shutil.rmtree(build_path)
        print(f"Deleted existing build directory: {build_path}")


def directory_handler(
    project_dir: str,
    build_dir: str,
    path: str,
    copy_from_src: bool,
    subdirs: Optional[Dict[str, str]],
    override: bool,
    append: bool,
    exclude: List[str],
    mode: int,
):
    if override and append:
        raise Exception(
            f"either override or append or neither. can't do both. for path: {path}"
        )

    base_path = Path(build_dir).joinpath(path)
    base_path.mkdir(parents=True, exist_ok=True)
    print(f"Created directory: {base_path}")

    if subdirs:
        for subdir_path in subdirs.values():
            subdir_full_path = base_path.joinpath(subdir_path)
            subdir_full_path.mkdir(parents=True, exist_ok=True)
            print(f"Created subdirectory: {subdir_full_path}")

    if copy_from_src:
        src_path = Path(project_dir).joinpath(path)
        if not src_path.exists():
            print(f"Project Path: {src_path} does not exist")
            return
        files = glob.glob(str(Path(src_path).joinpath("**/*")), recursive=True)
        for file_path in files:
            src_file_path = Path(file_path)
            if len(exclude) > 0:
                skip_file = False
                for ex_paths in exclude:
                    if src_file_path.match(ex_paths):
                        skip_file = True
                        break
                if skip_file:
                    continue
            if not src_file_path.is_file():
                continue
            # Determine the final destination inside the build directory
            dest_path = base_path.joinpath(src_file_path.relative_to(src_path))
            # Create destination directory if necessary
            dest_path.parent.mkdir(parents=True, exist_ok=True)

            if not (override or append):
                if dest_path.exists():
                    raise Exception(f"Some file already exists at {dest_path}")

            if append:
                append_file_contents(dest_path, src_file_path)
                print(f"Appended {src_file_path} to {dest_path}")
            else:
                # Copy file to the destination path
                shutil.copy2(src_file_path, dest_path)
                print(f"Copied {src_file_path} to {dest_path}")

    try:
        os.chmod(str(base_path.absolute()), mode)
        print(f"Permissions for '{base_path}' set to {oct(mode)}")
    except Exception as e:
        print(f"Failed to set permissions for {base_path}: {e}")


def file_handler(
    project_dir: str,
    build_dir: str,
    path: str,
):
    src_path = Path(project_dir).joinpath(path)
    dest_path = Path(build_dir).joinpath(path)

    if not (src_path.exists() and src_path.is_file()):
        print(f"Invalid file path: {src_path}")
        return

    # Create destination directory if necessary
    dest_path.parent.mkdir(parents=True, exist_ok=True)

    # Copy file to the destination path
    shutil.copy2(src_path, dest_path)
    print(f"Copied {src_path} to {dest_path}")


def env_handler(project_dir: str, build_dir: str, src: str, dest: str):
    src_path = Path(project_dir).joinpath(src)
    dest_path = Path(build_dir).joinpath(dest)

    if not (src_path.exists() and src_path.is_file()):
        print(f"Invalid file path: {src_path}")
        return

    # Create destination directory if necessary
    dest_path.parent.mkdir(parents=True, exist_ok=True)

    if dest_path.exists() and dest_path.is_file():
        env1 = parse_env_file(dest_path)
        env2 = parse_env_file(src_path)

        # Merge dictionaries, with env2 overriding env1
        merged_env = {**env1, **env2}

        # Write merged result to output file
        with open(dest_path, "w") as file:
            for key, value in merged_env.items():
                file.write(f"{key}={value}\n")

        print(f"Merged .env file saved as {dest_path}")
    else:
        # Copy file to the destination path
        shutil.copy2(src_path, dest_path)
        print(f"Copied {src_path} to {dest_path}")


def yml_handler(
    project_dir: str,
    build_dir: str,
    path: str,
):
    src_path = Path(project_dir).joinpath(path)
    dest_path = Path(build_dir).joinpath(path)

    if not (src_path.exists() and src_path.is_file()):
        print(f"Invalid file path: {src_path}")
        return

    # Create destination directory if necessary
    dest_path.parent.mkdir(parents=True, exist_ok=True)

    if dest_path.exists() and dest_path.is_file():
        with open(src_path, "r") as file:
            src_yaml = yaml.safe_load(file)
            if src_yaml is None:
                src_yaml = {}
        with open(dest_path, "r") as file:
            dest_yaml = yaml.safe_load(file)
            if dest_yaml is None:
                dest_yaml = {}

        # Merge dictionaries, with src overriding dest
        merged_yaml = dict_deep_merge(dest_yaml, src_yaml)

        # Write merged result to output file
        with open(dest_path, "w") as file:
            yaml.safe_dump(merged_yaml, file)

        print(f"Merged docker-compose file saved as {dest_path}")
    else:
        # Copy file to the destination path
        shutil.copy2(src_path, dest_path)
        print(f"Copied {src_path} to {dest_path}")


def parse_cron_commands(file_path: str) -> List[str]:
    commands = []
    with open(file_path, "r") as f:
        for line in f:
            stripped_line = line.strip()
            # Skip empty lines
            if not stripped_line:
                continue
            # Skip comment lines
            if stripped_line.startswith("#"):
                continue
            else:
                # Collect command lines
                commands.append(stripped_line)
    return commands


def cron_file_handler(
    project_dir: str,
    build_dir: str,
    path: str,
):
    src_path = Path(project_dir).joinpath(path)
    dest_path = Path(build_dir).joinpath(path)

    if not (src_path.exists() and src_path.is_file()):
        print(f"Invalid file path: {src_path}")
        return

    # Create destination directory if necessary
    dest_path.parent.mkdir(parents=True, exist_ok=True)

    commands = []
    header_comment = "# min hour day month weekday command"
    # Copy file to the destination path
    if dest_path.exists() and dest_path.is_file():
        commands += parse_cron_commands(dest_path)

    # Copy src file cron jobs
    commands += parse_cron_commands(src_path)
    # Write merged result to output file
    with open(dest_path, "w") as f:
        f.write(header_comment + "\n")  # Write the header comment
        for command in commands:
            f.write(command + "\n")

    print(f"Merged cron.txt file saved as {dest_path}")


def main(project_dir: str, build_dir: str, config_file: str):
    project_dir = str(Path(project_dir).absolute())
    build_dir = str(Path(build_dir).absolute())
    config_file = str(Path(project_dir).joinpath(config_file).absolute())

    config_file_path = Path(project_dir).joinpath(config_file)
    if not (config_file_path.exists() and os.access(config_file_path, os.R_OK)):
        return

    print(
        f"Building project: {project_dir}, using config: {config_file} at build drop: {build_dir}"
    )

    config = load_config(config_file)

    for root_dir_name, cfg in config.items():
        build_root_dir = str(Path(build_dir).joinpath(root_dir_name).absolute())
        for name, params in cfg.items():
            path_type = params["type"]
            if path_type == "dir":
                directory_handler(
                    project_dir=project_dir,
                    build_dir=build_root_dir,
                    path=params["path"],
                    copy_from_src=params.get("copy_from_src", False),
                    subdirs=params.get("subdirs"),
                    override=False if name == "core" else params.get("override", False),
                    append=params.get("append", False),
                    exclude=list(params.get("exclude", {}).values()),
                    mode=int(params.get("mode", "0o775"), 8),
                )
            elif path_type == "file":
                file_handler(
                    project_dir=project_dir,
                    build_dir=build_root_dir,
                    path=params["path"],
                )
            elif path_type == "env":
                if params.get("target") is not None:
                    # we will skip processing of envs with target specified
                    # since they need to be applied in the end and only in the
                    # exchange level module. We will process them after the build
                    # process is done explicitly.
                    continue
                env_handler(
                    project_dir=project_dir,
                    build_dir=build_root_dir,
                    src=params["path"],
                    dest=params.get("dest", params["path"]),
                )
            elif path_type == "yml":
                yml_handler(
                    project_dir=project_dir,
                    build_dir=build_root_dir,
                    path=params["path"],
                )
            elif path_type == "crontxt":
                cron_file_handler(
                    project_dir=project_dir,
                    build_dir=build_root_dir,
                    path=params["path"],
                )
            else:
                raise ValueError(f"Path Type: {path_type} is not supported")


def render_template(
    template_path: str,
    output_path: str,
    env_dict: dict,
) -> None:
    """
    Renders a template using Jinja2 with provided placeholders and writes the result to a file.
    """
    if not os.path.exists(template_path):
        print(f"Template does not exist at path: {template_path}, skipping.")
        return

    # Read template
    with open(template_path, "r") as file:
        template_str = file.read()
    rendered = jinja2.Template(template_str, undefined=jinja2.StrictUndefined).render(
        **env_dict
    )

    # Write the rendered to output file
    with open(output_path, "w") as file:
        file.write(rendered)

    print(f"rendered file '{output_path}' generated.")

    os.remove(template_path)
    print(f"template file '{template_path}' removed.")


def build_templates(project_dir: str, build_dir: str, config_file: str):
    project_dir = str(Path(project_dir).absolute())
    build_dir = str(Path(build_dir).absolute())
    config_file = str(Path(project_dir).joinpath(config_file).absolute())

    env_file_path = str(Path(build_dir).joinpath("src/.env").absolute())
    env_dict = dict(dotenv_values(env_file_path))

    config_file_path = Path(project_dir).joinpath(config_file)
    if not (config_file_path.exists() and os.access(config_file_path, os.R_OK)):
        return

    config = load_config(config_file)
    
    if config is None:
        print("No Templates Config Provided")
        return

    for root_dir_name, cfg in config.items():
        build_root_dir = str(Path(build_dir).joinpath(root_dir_name).absolute())
        for name, params in cfg.items():
            path_type = params["type"]
            if path_type == "jinja":
                src_file_path = Path(build_root_dir).joinpath(params["path"])
                dest_file_path = Path(build_root_dir).joinpath(params["dest"])
                EXCHANGE_TYPE = env_dict["EXCHANGE_TYPE"]
                EXECUTION_LEVEL = env_dict["EXECUTION_LEVEL"]
                # unique and deterministic uint32 server-id for mysql based on the string: {EXCHANGE_TYPE}_{EXECUTION_LEVEL}
                docker_project_id = xxhash.xxh32(
                    f"{EXCHANGE_TYPE}_{EXECUTION_LEVEL}", seed=0
                ).intdigest()
                if "docker_project_id" in env_dict:
                    raise Exception(
                        "`docker_project_id` is a reserved environment variable. Do not define this in .env file."
                    )
                render_template(
                    src_file_path,
                    dest_file_path,
                    env_dict | {"docker_project_id": docker_project_id},
                )
            else:
                raise ValueError(f"Path Type: {path_type} is not supported")

    print("Templates Rendering Completed")


def override_build_target_env(project_dir: str, build_dir: str, config_file: str):
    project_dir = str(Path(project_dir).absolute())
    build_dir = str(Path(build_dir).absolute())
    config_file = str(Path(project_dir).joinpath(config_file).absolute())

    config_file_path = Path(project_dir).joinpath(config_file)
    if not (config_file_path.exists() and os.access(config_file_path, os.R_OK)):
        return

    config = load_config(config_file)

    for root_dir_name, cfg in config.items():
        build_root_dir = str(Path(build_dir).joinpath(root_dir_name).absolute())
        for name, params in cfg.items():
            path_type = params["type"]
            if path_type == "env":
                if (params.get("target") is not None) and (
                    params["target"] == os.environ.get("BUILD_TARGET")
                ):
                    env_handler(
                        project_dir=project_dir,
                        build_dir=build_root_dir,
                        src=params["path"],
                        dest=params.get("dest", params["path"]),
                    )
                    print(
                        f"Override completed for build target: {os.environ.get('BUILD_TARGET')}"
                    )


class RecursionDirection(enum.Enum):
    TOP_DOWN = 1
    BOTTOM_UP = 2


def recurse_git_submodules(
    project_dir: str,
    build_dir: str,
    func: Callable[[str, str, str], None],
    direction: RecursionDirection,
    *args,
    **kwargs,
):
    submodules = (
        subprocess.check_output(
            ["git", "submodule", "foreach", "--quiet", "echo $path"], cwd=project_dir
        )
        .decode("utf-8")
        .splitlines()
    )

    if direction == RecursionDirection.TOP_DOWN:
        func(project_dir, build_dir, *args, **kwargs)

    for submodule in submodules:
        submodule_path = Path(project_dir).joinpath(submodule)
        recurse_git_submodules(
            str(submodule_path.absolute()), build_dir, func, direction, *args, **kwargs
        )

    if direction == RecursionDirection.BOTTOM_UP:
        func(project_dir, build_dir, *args, **kwargs)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Build Script")

    # Add arguments
    parser.add_argument("--src", type=str, help="Project Directory")
    parser.add_argument("--build", type=str, help="Build Directory")

    # Parse the arguments
    args = parser.parse_args()
    if not (args.src and args.build):
        raise ValueError("Provide cmdline arguments please")

    print(args)

    # Delete the build directory if it exists
    delete_build_directory(args.build)

    recurse_git_submodules(
        project_dir=args.src,
        build_dir=args.build,
        func=main,
        direction=RecursionDirection.BOTTOM_UP,
        config_file="build.yml",
    )

    if os.environ.get("BUILD_TARGET") is not None:
        override_build_target_env(
            project_dir=args.src,
            build_dir=args.build,
            config_file="build.yml",
        )

    recurse_git_submodules(
        project_dir=args.src,
        build_dir=args.build,
        func=build_templates,
        direction=RecursionDirection.TOP_DOWN,
        config_file="templates.yml",
    )
