import pytest
from unittest.mock import Mock, patch, AsyncMock
import pandas as pd
from toti.rpc import datasnapservice_old_pb2 as datasnap_service_pb2
import fakeredis.aioredis as fkaioredis

# Patch balte_initializer and balte_config before importing the module under test
with patch('balte.utility_inbuilt.balte_initializer'), \
     patch('balte.balte_config') as mock_balte_config_module:
    mock_balte_config_module.symbol_to_balte_id = {
        'NIFTY': 5001,
        'BANKNIFTY': 5002,
        'CRUDEOIL': 7011,
    }
    mock_balte_config_module.MARKET_CLOSE_HOUR = 15
    mock_balte_config_module.MARKET_CLOSE_MINUTE = 30
    mock_balte_config_module.TIMEZONE = 'Asia/Kolkata'

    from exchange.runners.redis_data_handler import RedisDataHandlerRunner


@pytest.fixture
def mock_redis_client(mocker):
    """Fixture to provide a Redis client backed by fakeredis."""
    fake_redis = fkaioredis.FakeRedis(decode_responses=True)
    mocker.patch(
        "redis.asyncio.from_url",
        return_value=fake_redis,
    )
    return fake_redis


@pytest.fixture
def mock_get_local_timestamp():
    """Fixture to mock get_local_timestamp method."""
    with patch('exchange.runners.redis_data_handler.RedisDataHandlerRunner.get_local_timestamp') as mock_timestamp:
        mock_timestamp.return_value = pd.Timestamp('2023-01-01 15:30:00')
        yield mock_timestamp


@pytest.fixture
def mock_datasnap_client():
    """Fixture to provide a mocked DataSnap client."""
    mock_client = Mock()
    mock_client.get_latest_second = Mock()
    mock_client.get_tick_data = Mock()
    mock_client.close = Mock()
    return mock_client


@pytest.fixture
def redis_data_handler(mock_datasnap_client):
    """Fixture to provide a RedisDataHandlerRunner instance with mocked dependencies."""
    with patch('exchange.runners.redis_data_handler.DataSnapClient') as mock_datasnap_class, \
         patch('balte.utility_inbuilt.balte_initializer') as mock_initializer:
             
        mock_datasnap_class.return_value = mock_datasnap_client
        mock_initializer.return_value = None
        handler = RedisDataHandlerRunner()
        handler.datasnap_client = mock_datasnap_client
        return handler


class TestRedisDataHandlerRunnerInitialization:
    """Test cases for RedisDataHandlerRunner initialization."""

    def test_init_sets_redis_connection_params(self, redis_data_handler):
        """Test that initialization sets Redis connection parameters."""
        assert redis_data_handler.redis_host == 'REDIS_HOST'
        assert redis_data_handler.redis_port == 1
        assert redis_data_handler.redis_db == ""
        assert redis_data_handler.redis_client is None

    def test_init_creates_datasnap_client(self, redis_data_handler, mock_datasnap_client):
        """Test that initialization creates a DataSnap client."""
        assert redis_data_handler.datasnap_client == mock_datasnap_client

    def test_init_sets_market_closing_time(self, redis_data_handler):
        """Test that initialization sets market closing time correctly."""
        with patch('pandas.Timestamp.now') as mock_now, \
             patch('balte.balte_config.TIMEZONE', 'Asia/Kolkata'):
            
            mock_timestamp = pd.Timestamp('2023-01-01 10:00:00')
            mock_now.return_value.replace.return_value = mock_timestamp

            expected_time = redis_data_handler.get_local_timestamp().normalize().replace(
                hour=15, minute=30
            )
            assert redis_data_handler.market_closing_time.hour == expected_time.hour
            assert redis_data_handler.market_closing_time.minute == expected_time.minute


class TestRedisDataHandlerRunnerStaticMethods:
    """Test cases for static methods."""

    def test_get_local_timestamp_returns_timestamp(self):
        """Test that get_local_timestamp returns a pandas Timestamp."""
        with patch('pandas.Timestamp.now') as mock_now, \
             patch('balte.balte_config.TIMEZONE', 'Asia/Kolkata'):
            mock_timestamp = pd.Timestamp('2023-01-01 10:00:00')
            mock_now.return_value.replace.return_value = mock_timestamp

            result = RedisDataHandlerRunner.get_local_timestamp()

            mock_now.assert_called_once_with(tz='Asia/Kolkata')
            assert result == mock_timestamp

    def test_parse_contract_with_options(self):
        """Test parsing contract with options."""
        contract = "NIFTY31-Jul-2025CE24000"
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(contract)
        
        assert symbol == "NIFTY"
        assert expiry == "31-Jul-2025"
        assert option_type == "CE"
        assert strike == "24000"

    def test_parse_contract_with_futures(self):
        """Test parsing contract with futures."""
        contract = "GOLD31-Dec-2023"
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(contract)
        
        assert symbol == "GOLD"
        assert expiry == "31-Dec-2023"
        assert option_type == ""
        assert strike == ""

    def test_parse_contract_spot_only(self):
        """Test parsing contract with spot only."""
        contract = "NIFTY"
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(contract)
        
        assert symbol == "NIFTY"
        assert expiry == ""
        assert option_type == ""
        assert strike == ""

    def test_contract_name_to_balte_id_spot(self):
        """Test contract_name_to_balte_id for spot contracts."""
        result = RedisDataHandlerRunner.contract_name_to_balte_id("mcx_spot", "NIFTY")
        assert result == 1234

    def test_contract_name_to_balte_id_futures(self):
        """Test contract_name_to_balte_id for futures contracts."""
        result = RedisDataHandlerRunner.contract_name_to_balte_id(
            "mcx_fut_near", "GOLD31-Dec-2023"
        )
        expected = int("2023123121234")  # expiry_code + type_code + underlying_code
        assert result == expected

    def test_contract_name_to_balte_id_options_optcom(self):
        """Test contract_name_to_balte_id for optcom options."""
        result = RedisDataHandlerRunner.contract_name_to_balte_id(
            "optcom", "NIFTY31-Dec-2023CE24000"
        )
        # strike//10 + expiry_code + type_code + underlying_code
        expected = int("240020231231" + "1" + "1234")
        assert result == expected


class TestRedisDataHandlerRunnerCaching:
    """Test cases for caching functionality."""

    def test_get_balte_id_caches_result(self, redis_data_handler):
        """Test that get_balte_id caches the result."""
        contract = "CRUDEOIL17-Jul-2025CE595000"

        result1 = redis_data_handler.get_balte_id(contract)
        assert result1 == 595002025071717011
        assert redis_data_handler.contract_to_balte_id_cache[contract] == 595002025071717011

    def test_clear_balte_id_cache(self, redis_data_handler):
        """Test clearing the balte_id cache."""
        redis_data_handler.contract_to_balte_id_cache["TEST"] = 123
        redis_data_handler.clear_balte_id_cache()
        assert redis_data_handler.contract_to_balte_id_cache == {}


class TestRedisDataHandlerRunnerRedisOperations:
    """Test cases for Redis operations."""

    @pytest.mark.asyncio
    async def test_connect_to_redis_creates_client(self, redis_data_handler, mock_redis_client):
        """Test that connect_to_redis creates a Redis client."""
        # The mock_redis_client fixture already patches redis.asyncio.from_url
        await redis_data_handler.connect_to_redis()

        assert redis_data_handler.redis_client is not None

    @pytest.mark.asyncio
    async def test_connect_to_redis_skips_if_already_connected(self, redis_data_handler):
        """Test that connect_to_redis skips if already connected."""
        mock_client = AsyncMock()
        redis_data_handler.redis_client = mock_client
        
        with patch('redis.asyncio.from_url') as mock_from_url:
            await redis_data_handler.connect_to_redis()
            mock_from_url.assert_not_called()

    @pytest.mark.asyncio
    async def test_store_balte_id_to_ltp_in_redis_success(self, redis_data_handler, mock_redis_client):
        """Test successful storage of balte_id to LTP mappings."""
        redis_data_handler.redis_client = mock_redis_client
        tick_data = [
            "1|NIFTY|field2|100.50|field4",
            "2|BANKNIFTY|field2|200.75|field4"
        ]

        await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)

        nifty_balte_id = redis_data_handler.get_balte_id("NIFTY")
        banknifty_balte_id = redis_data_handler.get_balte_id("BANKNIFTY")

        nifty_ltp = await mock_redis_client.get(str(nifty_balte_id))
        banknifty_ltp = await mock_redis_client.get(str(banknifty_balte_id))

        assert nifty_ltp == "100.50"
        assert banknifty_ltp == "200.75"

    @pytest.mark.asyncio
    async def test_store_balte_id_to_ltp_raises_if_no_client(self, redis_data_handler):
        """Test that store_balte_id_to_ltp raises exception if Redis client not initialized."""
        tick_data = ["1|NIFTY|field2|100.50|field4"]
        
        with pytest.raises(Exception, match="Redis client is not initialized"):
            await redis_data_handler.store_balte_id_to_ltp_in_redis(tick_data)


class TestRedisDataHandlerRunnerMarketOperations:
    """Test cases for market operations."""

    @pytest.mark.asyncio
    async def test_market_termination_checker_sets_flag(self, redis_data_handler):
        """Test that market_termination_checker sets the market closed flag."""
        with patch("pandas.Timestamp.now") as mock_now, \
             patch("balte.balte_config.TIMEZONE", "Asia/Kolkata"):
            mock_timestamp = pd.Timestamp('2023-01-01 10:00:00')
            mock_now.return_value.replace.return_value = mock_timestamp

            redis_data_handler.market_closing_time = redis_data_handler.get_local_timestamp() - pd.Timedelta(seconds=1)

            await redis_data_handler.market_termination_checker()

            assert redis_data_handler.is_market_closed.done()
            assert redis_data_handler.is_market_closed.result() is True

    def test_run_method(self, redis_data_handler):
        """Test the synchronous run method."""
        with patch.object(redis_data_handler, 'fetch_and_store_data') as mock_fetch, \
             patch.object(redis_data_handler, 'market_termination_checker') as mock_termination:
            mock_fetch.return_value = None
            mock_termination.return_value = None

            redis_data_handler.run()

            mock_fetch.assert_called_once()
            mock_termination.assert_called_once()
