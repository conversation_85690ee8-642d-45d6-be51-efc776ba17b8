import time
import logging
from typing import cast
import grpc
from exchange.helpers.configstore import DATASNAP_HOST, DATASNAP_PORT
from toti.rpc import datasnapservice_old_pb2 as datasnap_service_pb2
from exchange.helpers.redis_data_handler_components.rpc import datasnap_service_pb2_grpc_env_0_0_3


class DataSnapClient:
    def __init__(self, logger: logging.Logger) -> None:
        """
        Initialize the gRPC datasnap client.
        """
        self.logger = logger
        self.channel = grpc.insecure_channel(f"{DATASNAP_HOST}:{DATASNAP_PORT}")
        self.stub = datasnap_service_pb2_grpc_env_0_0_3.DataSnapStub(self.channel)
        self.logger.info(f"DataSnapClient initialized with connection to {DATASNAP_HOST}:{DATASNAP_PORT}")

    def get_tick_data(
        self, segment: str, timestamp: str, encoding: str = ""
    ) -> datasnap_service_pb2.RepeatedArrayReply:
        """
        Get tick data for a specific segment and timestamp.

        Args:
            segment (str): The market segment
            timestamp (str): The timestamp to query
            encoding (str): Optional encoding parameter

        Returns:
            datasnap_service_pb2.RepeatedArrayReply: Array of strings containing the tick data
        """
        while True:
            try:
                request = datasnap_service_pb2.DataSnapRequest(
                    segment=segment, timestamp=timestamp, encoding=encoding
                )
                response = cast(
                    datasnap_service_pb2.RepeatedArrayReply,
                    self.stub.get_tick_data(request),
                )
                return response
            except grpc.RpcError as e:
                self.logger.error(f"Error in get_tick_data for segment {segment}, timestamp {timestamp}: {e}")
                self.logger.info("Retrying get_tick_data in 5 seconds...")
                time.sleep(5)

    def get_latest_second(self, segment: str) -> datasnap_service_pb2.TimestampResponse:
        """
        Get the latest available second for a market segment.

        Args:
            segment (str): The market segment

        Returns:
            datasnap_service_pb2.TimestampResponse: The latest timestamp available
        """
        while True:
            try:
                request = datasnap_service_pb2.TimeStampRequest(segment=segment)
                response = cast(
                    datasnap_service_pb2.TimestampResponse,
                    self.stub.get_latest_second(request),
                )
                return response
            except grpc.RpcError as e:
                self.logger.error(f"Error in get_latest_second for segment {segment}: {e}")
                self.logger.info("Retrying get_latest_second in 5 seconds...")
                time.sleep(5)

    def close(self) -> None:
        """Close the gRPC channel"""
        self.logger.info("Closing DataSnap gRPC channel")
        self.channel.close()
