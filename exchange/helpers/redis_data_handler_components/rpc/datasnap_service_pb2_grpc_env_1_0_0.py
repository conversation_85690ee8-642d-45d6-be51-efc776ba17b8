# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from toti.rpc import datasnapservice_new_pb2 as datasnap__service__pb2


class DataSnapStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.get_tick_data = channel.unary_unary(
                '/datasnapservice.DataSnap/get_tick_data',
                request_serializer=datasnap__service__pb2.DataSnapRequest.SerializeToString,
                response_deserializer=datasnap__service__pb2.RepeatedArrayReply.FromString,
                )
        self.get_latest_second = channel.unary_unary(
                '/datasnapservice.DataSnap/get_latest_second',
                request_serializer=datasnap__service__pb2.TimeStampRequest.SerializeToString,
                response_deserializer=datasnap__service__pb2.TimestampResponse.FromString,
                )


class DataSnapServicer(object):
    """Missing associated documentation comment in .proto file."""

    def get_tick_data(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def get_latest_second(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DataSnapServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'get_tick_data': grpc.unary_unary_rpc_method_handler(
                    servicer.get_tick_data,
                    request_deserializer=datasnap__service__pb2.DataSnapRequest.FromString,
                    response_serializer=datasnap__service__pb2.RepeatedArrayReply.SerializeToString,
            ),
            'get_latest_second': grpc.unary_unary_rpc_method_handler(
                    servicer.get_latest_second,
                    request_deserializer=datasnap__service__pb2.TimeStampRequest.FromString,
                    response_serializer=datasnap__service__pb2.TimestampResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'datasnapservice.DataSnap', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DataSnap(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def get_tick_data(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/datasnapservice.DataSnap/get_tick_data',
            datasnap__service__pb2.DataSnapRequest.SerializeToString,
            datasnap__service__pb2.RepeatedArrayReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def get_latest_second(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/datasnapservice.DataSnap/get_latest_second',
            datasnap__service__pb2.TimeStampRequest.SerializeToString,
            datasnap__service__pb2.TimestampResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
